import React, { useState } from "react";
import { useForm } from "react-hook-form";
import {
  FaTimes,
  FaSlack,
  FaEye,
  FaEyeSlash,
  FaCheckCircle,
  FaExclamationTriangle,
  FaCog,
  FaPlus,
  FaTrash,
} from "react-icons/fa";
import { MdSave, MdSettings } from "react-icons/md";
import ConnectionTestPanel from "./ConnectionTestPanel";
import {
  useSlackConnect,
  useSlackDisconnect,
} from "../../../hooks/useSlackIntegration";

/**
 * Consolidated Slack integration modal
 * Combines connection setup, status display, and channel mapping configuration
 */
const SlackConnectionModal = ({ isOpen, onClose, currentStatus = null }) => {
  const [showToken, setShowToken] = useState(false);
  const [testResult, setTestResult] = useState(null);
  const [activeTab, setActiveTab] = useState("connection"); // 'connection' or 'channels'
  const [channelMappings, setChannelMappings] = useState([
    {
      id: 1,
      channel: "#daily-reports",
      reportType: "daily-work",
      enabled: true,
    },
    {
      id: 2,
      channel: "#shopify-updates",
      reportType: "shopify",
      enabled: true,
    },
  ]);

  const connectMutation = useSlackConnect();
  const disconnectMutation = useSlackDisconnect();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
  } = useForm();

  const botToken = watch("botToken");
  const isConnected = currentStatus?.connected;

  const handleFormSubmit = (data) => {
    connectMutation.mutate(data, {
      onSuccess: () => {
        reset();
        onClose();
      },
    });
  };

  const handleDisconnect = () => {
    if (
      window.confirm(
        "Are you sure you want to disconnect the Slack integration?"
      )
    ) {
      disconnectMutation.mutate(undefined, {
        onSuccess: () => {
          onClose();
        },
      });
    }
  };

  const handleClose = () => {
    reset();
    setTestResult(null);
    onClose();
  };

  const handleTestSuccess = (result) => {
    setTestResult({ success: true, data: result.data });
  };

  const handleTestError = (error) => {
    setTestResult({ success: false, error });
  };

  // Channel mapping functions
  const addChannelMapping = () => {
    const newMapping = {
      id: Date.now(),
      channel: "",
      reportType: "daily-work",
      enabled: true,
    };
    setChannelMappings([...channelMappings, newMapping]);
  };

  const removeChannelMapping = (id) => {
    setChannelMappings(channelMappings.filter((mapping) => mapping.id !== id));
  };

  const updateChannelMapping = (id, field, value) => {
    setChannelMappings(
      channelMappings.map((mapping) =>
        mapping.id === id ? { ...mapping, [field]: value } : mapping
      )
    );
  };

  const saveChannelMappings = () => {
    // TODO: Implement API call to save channel mappings
    console.log("Saving channel mappings:", channelMappings);
    // For now, just show success message
    alert("Channel mappings saved successfully!");
  };

  const getStatusIcon = () => {
    if (isConnected) {
      return <FaCheckCircle className="w-5 h-5 text-green-500" />;
    }
    return <FaExclamationTriangle className="w-5 h-5 text-red-500" />;
  };

  const getStatusText = () => {
    return isConnected ? "Connected" : "Disconnected";
  };

  const getStatusColor = () => {
    return isConnected
      ? "text-green-600 dark:text-green-400"
      : "text-red-600 dark:text-red-400";
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-40 transition-opacity"
        onClick={handleClose}
      />

      {/* Modal */}
      <div className="fixed inset-0 z-50 overflow-hidden">
        <div className="flex min-h-full items-center justify-center p-4">
          <div className="relative w-full max-w-2xl bg-white dark:bg-gray-800 rounded-2xl shadow-xl">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <FaSlack className="w-8 h-8 text-blue-600" />
                <div>
                  <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                    Slack Integration
                  </h2>
                  <div className="flex items-center gap-2 mt-1">
                    {getStatusIcon()}
                    <span className={`text-sm font-medium ${getStatusColor()}`}>
                      {getStatusText()}
                    </span>
                    {currentStatus?.teamName && (
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        • {currentStatus.teamName}
                      </span>
                    )}
                  </div>
                </div>
              </div>
              <button
                onClick={handleClose}
                className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <FaTimes className="w-5 h-5" />
              </button>
            </div>

            {/* Tabs */}
            <div className="flex border-b border-gray-200 dark:border-gray-700">
              <button
                onClick={() => setActiveTab("connection")}
                className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
                  activeTab === "connection"
                    ? "text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20"
                    : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <FaCog className="w-4 h-4" />
                  Connection Settings
                </div>
              </button>
              <button
                onClick={() => setActiveTab("channels")}
                className={`flex-1 px-6 py-3 text-sm font-medium transition-colors ${
                  activeTab === "channels"
                    ? "text-blue-600 border-b-2 border-blue-600 bg-blue-50 dark:bg-blue-900/20"
                    : "text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                }`}
              >
                <div className="flex items-center justify-center gap-2">
                  <MdSettings className="w-4 h-4" />
                  Channel Mapping
                </div>
              </button>
            </div>

            {/* Content */}
            <div className="p-6">
              {activeTab === "connection" && (
                <div className="space-y-6">
                  {/* Current Status */}
                  {isConnected && currentStatus && (
                    <div className="p-4 bg-green-50 border border-green-200 rounded-lg dark:bg-green-900/20 dark:border-green-800">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium text-green-800 dark:text-green-200">
                            Currently Connected
                          </div>
                          {currentStatus.teamName && (
                            <div className="text-sm text-green-700 dark:text-green-300">
                              Workspace: {currentStatus.teamName}
                            </div>
                          )}
                        </div>
                        <button
                          onClick={handleDisconnect}
                          disabled={disconnectMutation.isPending}
                          className="px-3 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
                        >
                          {disconnectMutation.isPending
                            ? "Disconnecting..."
                            : "Disconnect"}
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Form */}
                  <form
                    onSubmit={handleSubmit(handleFormSubmit)}
                    className="space-y-4"
                  >
                    {/* Bot Token Input */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Bot Token *
                      </label>
                      <div className="relative">
                        <input
                          type={showToken ? "text" : "password"}
                          {...register("botToken", {
                            required: "Bot token is required",
                            pattern: {
                              value: /^xoxb-/,
                              message: "Bot token must start with 'xoxb-'",
                            },
                          })}
                          className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                          placeholder="xoxb-your-bot-token-here"
                        />
                        <button
                          type="button"
                          onClick={() => setShowToken(!showToken)}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600"
                        >
                          {showToken ? <FaEyeSlash /> : <FaEye />}
                        </button>
                      </div>
                      {errors.botToken && (
                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                          {errors.botToken.message}
                        </p>
                      )}
                      <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                        Get your bot token from your Slack app settings
                      </p>
                    </div>

                    {/* Connection Test */}
                    <ConnectionTestPanel
                      botToken={botToken}
                      onTestSuccess={handleTestSuccess}
                      onTestError={handleTestError}
                    />

                    {/* Submit Button */}
                    <div className="flex gap-3 pt-4">
                      <button
                        type="button"
                        onClick={handleClose}
                        className="flex-1 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={
                          connectMutation.isPending || !testResult?.success
                        }
                        className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        {connectMutation.isPending ? (
                          <span className="loading loading-spinner loading-sm"></span>
                        ) : (
                          <MdSave />
                        )}
                        {connectMutation.isPending
                          ? "Connecting..."
                          : isConnected
                          ? "Update"
                          : "Connect"}
                      </button>
                    </div>
                  </form>

                  {/* Help Text */}
                  <div className="text-xs text-gray-500 dark:text-gray-400 space-y-2">
                    <p>
                      <strong>How to get your bot token:</strong>
                    </p>
                    <ol className="list-decimal list-inside space-y-1 ml-2">
                      <li>
                        Go to{" "}
                        <a
                          href="https://api.slack.com/apps"
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          api.slack.com/apps
                        </a>
                      </li>
                      <li>Select your app or create a new one</li>
                      <li>Go to "OAuth & Permissions"</li>
                      <li>Copy the "Bot User OAuth Token"</li>
                    </ol>
                  </div>
                </div>
              )}

              {activeTab === "channels" && (
                <div className="space-y-6">
                  {/* Channel Mapping Header */}
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                        Channel Mapping
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Configure which Slack channels receive notifications for
                        different types of reports
                      </p>
                    </div>
                    <button
                      onClick={addChannelMapping}
                      className="flex items-center gap-2 px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <FaPlus className="w-4 h-4" />
                      Add Mapping
                    </button>
                  </div>

                  {/* Channel Mappings List */}
                  <div className="space-y-3">
                    {channelMappings.map((mapping) => (
                      <div
                        key={mapping.id}
                        className="flex items-center gap-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg"
                      >
                        {/* Channel Input */}
                        <div className="flex-1">
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Slack Channel
                          </label>
                          <input
                            type="text"
                            value={mapping.channel}
                            onChange={(e) =>
                              updateChannelMapping(
                                mapping.id,
                                "channel",
                                e.target.value
                              )
                            }
                            placeholder="#channel-name"
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                          />
                        </div>

                        {/* Report Type Select */}
                        <div className="flex-1">
                          <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Report Type
                          </label>
                          <select
                            value={mapping.reportType}
                            onChange={(e) =>
                              updateChannelMapping(
                                mapping.id,
                                "reportType",
                                e.target.value
                              )
                            }
                            className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                          >
                            <option value="daily-work">
                              Daily Work Reports
                            </option>
                            <option value="shopify">Shopify Reports</option>
                            <option value="meeting-summary">
                              Meeting Summaries
                            </option>
                            <option value="task-updates">Task Updates</option>
                          </select>
                        </div>

                        {/* Enable Toggle */}
                        <div className="flex items-center gap-2">
                          <label className="text-xs font-medium text-gray-700 dark:text-gray-300">
                            Enabled
                          </label>
                          <button
                            onClick={() =>
                              updateChannelMapping(
                                mapping.id,
                                "enabled",
                                !mapping.enabled
                              )
                            }
                            className={`
                              relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
                              ${
                                mapping.enabled
                                  ? "bg-blue-600"
                                  : "bg-gray-200 dark:bg-gray-600"
                              }
                            `}
                          >
                            <span
                              className={`
                                inline-block h-3 w-3 transform rounded-full bg-white transition-transform duration-200
                                ${
                                  mapping.enabled
                                    ? "translate-x-5"
                                    : "translate-x-1"
                                }
                              `}
                            />
                          </button>
                        </div>

                        {/* Remove Button */}
                        <button
                          onClick={() => removeChannelMapping(mapping.id)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-md transition-colors"
                        >
                          <FaTrash className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>

                  {/* Save Button */}
                  <div className="flex justify-end pt-4 border-t border-gray-200 dark:border-gray-700">
                    <button
                      onClick={saveChannelMappings}
                      className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                    >
                      <MdSave className="w-4 h-4" />
                      Save Channel Mappings
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SlackConnectionModal;
